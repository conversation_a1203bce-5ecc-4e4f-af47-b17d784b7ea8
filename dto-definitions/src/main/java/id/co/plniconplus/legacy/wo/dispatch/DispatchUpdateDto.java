package id.co.plniconplus.legacy.wo.dispatch;

import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 10/09/2025
 */
@RegisterForReflection
public record DispatchUpdateDto(
        String status,
        LocalDateTime statusDate,
        String noWo,
        LocalDateTime tanggalWo,
        LocalDateTime tanggalWoExpire,
        String kodeVendor,
        String idUser) {
}
