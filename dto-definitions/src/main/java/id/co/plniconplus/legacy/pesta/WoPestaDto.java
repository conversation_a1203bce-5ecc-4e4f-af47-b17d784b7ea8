package id.co.plniconplus.legacy.pesta;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 04/09/2025
 */
@RegisterForReflection(targets = {
        WoPestaDto.class,
        WoPestaDto.WoPestaDtoBuilder.class
})
@Builder
public record WoPestaDto(
        LocalDateTime tanggalMulai,
        LocalDateTime tanggalSelesai,
        String lwbpSebelumPesta,
        String wbpSebelumPesta,
        String kvarhSebelumPesta,
        String sisaKwhSebelumPesta,
        LocalDateTime tanggalLunas,
        String email,
        String petugasPenerima,
        String tarif) {
}
