package id.co.plniconplus.legacy.wo.dispatch;

import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.ws.rs.QueryParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 23/09/2025
 */
@RegisterForReflection
public record DispatchRequestDto(
        @QueryParam("pUnitup") String unitUp,
        @QueryParam("pJenisTanggal") String jenisTanggal,
        @QueryParam("pTanggalAwal") String tanggalAwal,
        @QueryParam("pTanggalAkhir") String tanggalAkhir,
        @QueryParam("pVendor") String kodeVendor,
        @QueryParam("pJenisTransaksi") String jenisTransaksi,
        @QueryParam("pField") String field,
        @QueryParam("pKeyWords") String keyWord,
        @QueryParam("pAmi") String ami,
        @QueryParam("pStatusGcm") Integer gcmStatus) {
}
