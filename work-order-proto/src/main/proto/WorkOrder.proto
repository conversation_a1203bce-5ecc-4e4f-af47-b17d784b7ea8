syntax = "proto3";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.wo";
option java_outer_classname = "WorkOrderProto";
option objc_class_prefix = "WOD";

package id.co.plniconplus.legacy.wo;

service WorkOrderView {
  rpc GetAllByPage (WorkOrderRequest) returns (WorkOrderResponseList) {}
}

message WorkOrderRequest {
  int32 page = 1;
  int32 size = 2;
  WorkOrderSort sort = 3;
}

message WorkOrderSort {
  string noAgenda = 1;
  string unitUp = 2;
}

message WorkOrderResponseList {
  repeated WorkOrderResponse workOrders = 1;
}

message WorkOrderResponse {
}