package id.co.plniconplus.legecy.location;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 03/09/2025
 */
@ApplicationScoped
public class LocationRepo implements PanacheRepositoryBase<Location, String> {

    public Location findByNoAgendaAndStatus(String noAgenda, String status) {
        return find("noAgenda = ?1 and status = ?2", noAgenda, status).firstResult();
    }
}
