package id.co.plniconplus.legecy.location;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 03/09/2025
 */
public record LocationDto(
        String noAgenda,
        LocalDateTime tanggalLog,
        String status,
        String latitude,
        String longitude,
        String kodeVendor,
        String namaVendor,
        String kodePetugas,
        String namaPetugas,
        LocalDateTime tanggalInsert,
        String flagAp2t,
        LocalDateTime tanggalFlagAp2t) {

    public static LocationDto fromEntity(Location location) {
        return new LocationDto(
                location.noAgenda,
                location.tanggalLog,
                location.status,
                location.latitude,
                location.longitude,
                location.kodeVendor,
                location.namaVendor,
                location.kodePetugas,
                location.namaPetugas,
                location.tanggalInsert,
                location.flagAp2t,
                location.tanggalFlagAp2t);
    }
}
