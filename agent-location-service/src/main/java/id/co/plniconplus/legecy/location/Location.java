package id.co.plniconplus.legecy.location;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 03/09/2025
 */
@Entity
@Table(schema = "FSO", name = "TRANS_LOCATION")
public class Location extends PanacheEntityBase {

    @Id
    @Size(max = 30)
    @Column(length = 30, name = "NOAGENDA")
    public String noAgenda;

    @Column(name = "TGLLOG", columnDefinition = "DATE")
    public LocalDateTime tanggalLog;

    @Size(max = 30)
    @Column(length = 30, name = "STATUS")
    public String status;

    @Size(max = 30)
    @Column(length = 30, name = "LATITUDE")
    public String latitude;

    @Size(max = 30)
    @Column(length = 30, name = "LONGITUDE")
    public String longitude;

    @Size(max = 50)
    @Column(length = 50, name = "KODE_VENDOR_LOC")
    public String kodeVendor;

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_VENDOR_LOC")
    public String namaVendor;

    @Size(max = 50)
    @Column(length = 50, name = "KODE_PETUGAS_LOC")
    public String kodePetugas;

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_PETUGAS_LOC")
    public String namaPetugas;

    @Column(name = "TGL_INSERT_LOC", columnDefinition = "DATE")
    public LocalDateTime tanggalInsert;

    @Size(max = 1)
    @Column(length = 1, name = "FLAG_AP2T_LOC", columnDefinition = "CHAR")
    public String flagAp2t;

    @Column(name = "TGL_FLAG_AP2T_LOC", columnDefinition = "DATE")
    public LocalDateTime tanggalFlagAp2t;
}
