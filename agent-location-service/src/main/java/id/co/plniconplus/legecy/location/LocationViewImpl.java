package id.co.plniconplus.legecy.location;

import id.co.plniconplus.legacy.location.AgentLocationRequest;
import id.co.plniconplus.legacy.location.AgentLocationResponse;
import id.co.plniconplus.legacy.location.AgentLocationView;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 25/09/2025
 */
@GrpcService
@RequiredArgsConstructor
public class LocationViewImpl implements AgentLocationView {

    private final LocationRepo locationRepo;

    @Override
    public Uni<AgentLocationResponse> getByNoAgendaAndStatus(AgentLocationRequest request) {
        Log.info("Received request for getByNoAgendaAndStatus");

        return Uni.createFrom().item(() -> locationRepo.findByNoAgendaAndStatus(request.getNoAgenda(), request.getStatus()))
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().ifNull()
                .failWith(() -> new StatusRuntimeException(Status.NOT_FOUND
                        .withDescription("Location with status " + request.getStatus()
                                + " and no agenda " + request.getNoAgenda() + " not found")))
                .map(location -> {
                    AgentLocationResponse.Builder builder = AgentLocationResponse.newBuilder();
                    builder.setNoAgenda(location.noAgenda)
                            .setTanggalLog(location.tanggalLog.toString())
                            .setStatus(location.status)
                            .setLatitude(location.latitude)
                            .setLongitude(location.longitude)
                            .setKodeVendor(location.kodeVendor)
                            .setNamaVendor(location.namaVendor)
                            .setKodePetugas(location.kodePetugas)
                            .setNamaPetugas(location.namaPetugas)
                            .setTanggalInsert(location.tanggalInsert.toString())
                            .setFlagAp2T(location.flagAp2t)
                            .setTanggalFlagAp2T(location.tanggalFlagAp2t.toString());
                    return builder.build();
                });
    }
}
