quarkus:
  http:
    root-path: /api
    port: ${AGENT_LOCATION_SERVICE_PORT:8081}
  hibernate-orm:
    dialect: org.hibernate.community.dialect.OracleLegacyDialect
    log:
      sql: true
      bind-parameters: true

"%test":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ***********************************
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${AGENT_LOCATION_GRPC_HOST}
        port: ${AGENT_LOCATION_GRPC_PORT}
        enable-reflection-service: true

"%dev":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: jdbc:oracle:thin:@*************:1521/FSODEV
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${AGENT_LOCATION_GRPC_HOST}
        port: ${AGENT_LOCATION_GRPC_PORT}
        enable-reflection-service: true

"%prod":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ${DATASOURCE_JDBC_URL}
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    hibernate-orm:
      log:
        sql: false
        bind-parameters: false
    grpc:
      server:
        host: ${AGENT_LOCATION_GRPC_HOST}
        port: ${AGENT_LOCATION_GRPC_PORT}
        enable-reflection-service: true
