package id.co.plniconplus.legacy.meter.prepaid;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 25/08/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_METER_PREPAID")
public class MeterPrepaid extends PanacheEntityBase {

    @EmbeddedId
    public MeterPrepaidId meterPrepaidId;

    @Size(max = 6)
    @Column(length = 6, name = "SUPPLY_GROUP_CODE", columnDefinition = "CHAR")
    public String supplyGroupCode;

    @Size(max = 2)
    @Column(length = 2, name = "VENDING_KEY_REGISTER", columnDefinition = "CHAR")
    public String vendingKeyRegister;

    @Size(max = 50)
    @Column(length = 50, name = "NAMA_VENDOR")
    public String vendorName;

    @Size(max = 50)
    @Column(length = 50, name = "ALAMAT1_VENDOR")
    public String vendorAddress1;

    @Size(max = 50)
    @Column(length = 50, name = "ALAMAT2_VENDOR")
    public String vendorAddress2;

    @Size(max = 50)
    @Column(length = 50, name = "ALAMAT3_VENDOR")
    public String vendorAddress3;

    @Size(max = 50)
    @Column(length = 50, name = "TELP_VENDOR")
    public String vendorPhone;

    @Size(max = 50)
    @Column(length = 50, name = "FACS_VENDOR")
    public String vendorFax;

    @Size(max = 50)
    @Column(length = 50, name = "PIC_VENDOR")
    public String vendorPic;

    @Size(max = 50)
    @Column(length = 50, name = "PIC_VENDOR_EMAIL")
    public String vendorPicEmail;

    @Column(name = "TGLCATAT",columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;
}
