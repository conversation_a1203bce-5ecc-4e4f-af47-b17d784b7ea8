package id.co.plniconplus.legacy.meter;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import static id.co.plniconplus.util.ResponseHandler.generateResponse;
import static jakarta.ws.rs.core.Response.Status.*;
import static jakarta.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/2/2025
 */
@Path("/v1/meter")
@RequiredArgsConstructor
@Tag(name = "Master meter")
public class MeterResource {

    private final MeterService meterService;

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Validate merek tipe meter")
    @APIResponse(responseCode = "200", description = "Meter validation result")
    @APIResponse(responseCode = "400", description = "Invalid parameters")
    @APIResponse(responseCode = "500", description = "Internal server error")
    public Uni<Response> validateMeter(@QueryParam("isPrepaid") @DefaultValue("false") boolean isPrepaid,
                                       @QueryParam("merekMeter") String merekMeter) {
        Log.debugv("Received request for validateMeter: isPrepaid={0}, merekMeter={1}",
                isPrepaid, merekMeter);

        return meterService.validateMeter(isPrepaid, merekMeter)
                .onItem().transform(isValid ->
                        Response.status(OK)
                                .entity(generateResponse(OK.getReasonPhrase(), isValid))
                                .build())
                .onFailure(ConstraintViolationException.class).recoverWithItem(e ->
                        Response.status(BAD_REQUEST)
                                .entity(generateResponse(BAD_REQUEST.getReasonPhrase(), e.getMessage()))
                                .build())
                .onFailure().recoverWithItem(e ->
                        Response.status(INTERNAL_SERVER_ERROR)
                                .entity(generateResponse(INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()))
                                .build());
    }
}
