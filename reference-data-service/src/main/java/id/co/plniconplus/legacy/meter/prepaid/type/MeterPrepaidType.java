package id.co.plniconplus.legacy.meter.prepaid.type;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 26/08/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_TYPE_METER_PREPAID")
public class MeterPrepaidType extends PanacheEntityBase {

    @EmbeddedId
    public MeterPrepaidTypeId meterPrepaidTypeId;

    @Size(max = 1)
    @Column(length = 1, name = "KODE_FASE", columnDefinition = "CHAR")
    public String kodeFase;

    @Column(name = "POWER_LIMIT", precision = 7, scale = 3)
    public BigDecimal powerLimit;

    @Size(max = 50)
    @Column(length = 50, name = "STANDAR_METER")
    public String standarMeter;

    @Size(max = 1)
    @Column(length = 1, name = "KODE_BLOK", columnDefinition = "CHAR")
    public String kodeBlok;

    @Column(name = "TANGGAL_BLOK", columnDefinition = "DATE")
    public LocalDateTime tanggalBlok;

    @Size(max = 100)
    @Column(length = 100, name = "KETERANGAN")
    public String keterangan;

    @Column(name = "TGLCATAT", columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;
}
