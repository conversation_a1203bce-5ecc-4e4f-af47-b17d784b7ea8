package id.co.plniconplus.legacy.meter;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 27/08/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_METER")
public class Meter extends PanacheEntityBase {

    @Id
    @Size(max = 12)
    @NotBlank
    @Column(length = 12, name = "MEREK_METER", nullable = false )
    public String merekMeter;

    @Size(max = 50)
    @Column(length = 50, name = "NAMA_PRODUSEN")
    public String namaProdusen;

    @Size(max = 50)
    @Column(length = 50, name = "LOKASI_PRODUSEN")
    public String lokasiProdusen;

    @Size(max = 100)
    @Column(length = 100, name = "ALAMAT_PRODUSEN")
    public String alamatProdusen;

    @Size(max = 300)
    @Column(length = 300, name = "PRODUK")
    public String produk;

    @Column(name = "TGLCATAT", columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;

    @Size(max = 30)
    @Column(length = 30, name = "PETUGASCATAT")
    public String petugasCatat;

    @Column(name = "AKTIF")
    public BigInteger aktif;

    @Column(name = "TGLEDIT", columnDefinition = "DATE")
    public LocalDateTime tanggalEdit;

    @Size(max = 30)
    @Column(length = 30, name = "PETUGASEDIT")
    public String petugasEdit;
}
