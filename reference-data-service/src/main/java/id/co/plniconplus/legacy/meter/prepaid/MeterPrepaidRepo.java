package id.co.plniconplus.legacy.meter.prepaid;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 26/08/2025
 */
@ApplicationScoped
public class MeterPrepaidRepo implements PanacheRepositoryBase<MeterPrepaid, String> {

    public Optional<MeterPrepaid> findByMerkMeter(String merkMeter) {
        return find("meterPrepaidId.merkMeter", merkMeter).firstResultOptional();
    }
}
