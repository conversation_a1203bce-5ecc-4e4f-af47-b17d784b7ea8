package id.co.plniconplus.legacy.meter.prepaid;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 22/09/2025
 */
@Embeddable
public class MeterPrepaidId implements Serializable {

    @Size(max = 2)
    @NotBlank
    @Column(length = 2, name = "KODE_PABRIKAN", nullable = false, columnDefinition = "CHAR")
    public String kodePabrikan;

    @Size(max = 10)
    @NotBlank
    @Column(length = 10, name = "MERK_METER", nullable = false)
    public String merkMeter;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        MeterPrepaidId that = (MeterPrepaidId) o;
        return Objects.equals(kodePabrikan, that.kodePabrikan) && Objects.equals(merkMeter, that.merkMeter);
    }

    @Override
    public int hashCode() {
        return Objects.hash(kodePabrikan, merkMeter);
    }
}
