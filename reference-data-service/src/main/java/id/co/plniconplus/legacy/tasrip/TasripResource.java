package id.co.plniconplus.legacy.tasrip;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;

import static id.co.plniconplus.util.ResponseHandler.generateResponse;
import static jakarta.ws.rs.core.Response.Status.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/16/2025
 */
@Path("/v1/tasrip")
@RequiredArgsConstructor
public class TasripResource {

    private final TasripService tasripService;

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Fetch tasrip with pagination")
    @APIResponse(responseCode = "200", description = "List of tasrip entries")
    @APIResponse(responseCode = "400", description = "Invalid pagination parameters")
    public Uni<Response> getTasripPaginated(
            @QueryParam("page") @DefaultValue("0") @Min(0) int page,
            @QueryParam("size") @DefaultValue("20") @Min(1) @Max(100) int size) {
        Log.debugv("Fetching tasrip - page: {0}, size: {1}", page, size);

        return tasripService.getAllByPage(page, size)
                .collect().asList()
                .onItem().transform(list ->
                        Response.status(OK)
                                .entity(generateResponse(OK.getReasonPhrase(), list))
                                .build())
                .onFailure(ConstraintViolationException.class).recoverWithItem(e ->
                        Response.status(BAD_REQUEST)
                                .entity(generateResponse(BAD_REQUEST.getReasonPhrase(), e.getMessage()))
                                .build())
                .onFailure().recoverWithItem(e ->
                        Response.status(INTERNAL_SERVER_ERROR)
                                .entity(generateResponse(INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()))
                                .build());
    }
}
