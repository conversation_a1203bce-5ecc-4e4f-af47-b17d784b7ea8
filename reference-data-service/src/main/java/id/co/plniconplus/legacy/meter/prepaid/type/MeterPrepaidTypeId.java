package id.co.plniconplus.legacy.meter.prepaid.type;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 22/09/2025
 */
@Embeddable
public class MeterPrepaidTypeId implements Serializable {

    @Size(max = 15)
    @NotBlank
    @Column(length = 15, name = "TYPE_METER", nullable = false)
    public String meterType;

    @Size(max = 2)
    @NotBlank
    @Column(length = 2, name = "KODE_PABRIKAN", nullable = false, columnDefinition = "CHAR")
    public String kodePabrikan;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        MeterPrepaidTypeId that = (MeterPrepaidTypeId) o;
        return Objects.equals(meterType, that.meterType) && Objects.equals(kodePabrikan, that.kodePabrikan);
    }

    @Override
    public int hashCode() {
        return Objects.hash(meterType, kodePabrikan);
    }
}
