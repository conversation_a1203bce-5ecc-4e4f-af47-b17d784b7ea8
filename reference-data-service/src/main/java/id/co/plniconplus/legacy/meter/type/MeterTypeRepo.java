package id.co.plniconplus.legacy.meter.type;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/2/2025
 */
@ApplicationScoped
public class MeterTypeRepo implements PanacheRepositoryBase<MeterType, String> {

    public Optional<MeterType> findByMerekMeter(String merekMeter) {
        return find("meterTypeId.merekMeter", merekMeter).firstResultOptional();
    }
}
