package id.co.plniconplus.legacy.meter.type;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/2/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_TYPE_METER")
public class MeterType extends PanacheEntityBase {

    @EmbeddedId
    public MeterTypeId meterTypeId;

    @Size(max = 1)
    @Column(length = 1, name = "KODE_FASE", columnDefinition = "CHAR")
    public String kodeFase;

    @Size(max = 200)
    @Column(length = 200, name = "SPESIFIKASI")
    public String spesifikasi;

    @Column(name = "TGLCATAT", columnDefinition = "DATE")
    public LocalDateTime tanggalCatat;

    @Size(max = 30)
    @Column(length = 30, name = "PETUGASCATAT")
    public String petugasCatat;

    @Size(max = 10)
    @Column(length = 10, name = "KELAS")
    public String kelas;

    @Size(max = 20)
    @Column(length = 20, name = "ARUS")
    public String arus;

    @Size(max = 30)
    @Column(length = 30, name = "PENGUKURAN")
    public String pengukuran;

    @Column(name = "AKTIF")
    public BigDecimal aktif;
}
