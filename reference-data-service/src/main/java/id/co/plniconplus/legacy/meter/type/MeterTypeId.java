package id.co.plniconplus.legacy.meter.type;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 22/09/2025
 */
@Embeddable
public class MeterTypeId implements Serializable {

    @Size(max = 15)
    @NotBlank
    @Column(length = 15, name = "TYPE_METER", nullable = false)
    public String tipeMeter;

    @Size(max = 12)
    @NotBlank
    @Column(length = 12, name = "MEREK_METER", nullable = false)
    public String merekMeter;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        MeterTypeId that = (MeterTypeId) o;
        return Objects.equals(tipeMeter, that.tipeMeter) && Objects.equals(merekMeter, that.merekMeter);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tipeMeter, merekMeter);
    }
}
