syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.location";
option java_outer_classname = "AgentLocation";
option objc_class_prefix = "LCT";

package id.co.plniconplus.legacy.location;

service AgentLocationView {
  rpc GetByNoAgendaAndStatus (AgentLocationRequest) returns (AgentLocationResponse) {}
}

message AgentLocationRequest {
  string noAgenda = 1;
  string status = 2;
}

message AgentLocationResponse {
  string noAgenda = 1;
  string tanggalLog = 2;
  string status = 3;
  string latitude = 4;
  string longitude = 5;
  string kodeVendor = 6;
  string namaVendor = 7;
  string kodePetugas = 8;
  string namaPetugas = 9;
  string tanggalInsert = 10;
  string flagAp2t = 11;
  string tanggalFlagAp2t = 12;
}