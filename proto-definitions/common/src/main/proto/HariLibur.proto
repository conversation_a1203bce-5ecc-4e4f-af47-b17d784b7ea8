syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.harilibur";
option java_outer_classname = "HariLiburProto";
option objc_class_prefix = "HLB";

package id.co.plniconplus.legacy.harilibur;

service HariLiburView {
  rpc GetListHariLibur (google.protobuf.Empty) returns (HariLiburResponseList);
  rpc GetListHariLiburBetween (HariLiburBetweenRequest) returns (HariLiburResponseList);
  rpc GetListHariLiburByDate (HariLiburRequest) returns (HariLiburResponseList);
}

message HariLiburBetweenRequest {
  string startDate = 1;
  string endDate = 2;
}

message HariLiburRequest {
  string date = 1;
}

message HariLiburResponse {
  string tanggalLibur = 1;
  string keteranganHariLibur = 2;
}

message HariLiburResponseList {
  repeated HariLiburResponse hariLiburList = 1;
}