syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.tasrip";
option java_outer_classname = "TasripProto";
option objc_class_prefix = "TRP";

package id.co.plniconplus.legacy.tasrip;

service TasripView {
  rpc GetTarifKwh (google.protobuf.Empty) returns (TarifKwhList) {}
}

message TarifKwh {
  string tarif = 1;
  string metKwh = 2;
}

message TarifKwhList {
  repeated TarifKwh tarifKwhList = 1;
}