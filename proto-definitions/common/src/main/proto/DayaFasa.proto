syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.dayafasa";
option java_outer_classname = "DayaFasaProto";
option objc_class_prefix = "DYF";

package id.co.plniconplus.legacy.dayafasa;

service DayaFasaView {
  rpc GetListDayaFasa (google.protobuf.Empty) returns (DayaFasaResponseList) {}
}

message DayaFasaResponse {
  int32 daya = 1;
  int32 fasa = 2;
}

message DayaFasaResponseList {
  repeated DayaFasaResponse dayaFasaList = 1;
}
