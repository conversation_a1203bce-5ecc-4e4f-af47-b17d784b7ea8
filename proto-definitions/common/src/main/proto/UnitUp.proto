syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "id.co.plniconplus.legacy.unit.up";
option java_outer_classname = "UnitUpProto";
option objc_class_prefix = "UUP";

package id.co.plniconplus.legacy.unit.up;

service UnitUpView {
  rpc GetListUnitUp (google.protobuf.Empty) returns (UnitUpResponseList) {}
}

message UnitUpResponse {
  string unitUp = 1;
  string namaUp = 2;
}

message UnitUpResponseList {
  repeated UnitUpResponse unitUpList = 1;
}