quarkus:
  http:
    root-path: /api
    port: ${WO_SERVICE_PORT:8082}
  hibernate-orm:
    dialect: org.hibernate.community.dialect.OracleLegacyDialect
    log:
      sql: true
      bind-parameters: true

  grpc:
    clients:
      unit-up:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
      daya-fasa:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
      gcm-status:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
      hari-libur:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
      tasrip:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
      agent-location:
        host: ${REF_DATA_GRPC_HOST}
        port: ${REF_DATA_GRPC_PORT}
  cache:
    caffeine:
      nama-up-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}
      daya-fasa-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}
      gcm-status-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}
      hari-libur-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}
      tarif-kwh-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}
      agent-location-cache:
        maximum-size: ${CAFFEINE_CACHE_MAX_SIZE}
        expire-after-write: ${CAFFEINE_CACHE_EXPIRE_AFTER_WRITE}

"%test":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ***********************************
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${WO_GRPC_HOST}
        port: ${WO_GRPC_PORT}
        enable-reflection-service: true

"%dev":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: jdbc:oracle:thin:@*************:1521/FSODEV
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    grpc:
      server:
        host: ${WO_GRPC_HOST}
        port: ${WO_GRPC_PORT}
        enable-reflection-service: true

"%prod":
  quarkus:
    datasource:
      db-kind: oracle
      db-version: 11.2.0
      jdbc:
        url: ${DATASOURCE_JDBC_URL}
      username: ${DATASOURCE_USERNAME}
      password: ${DATASOURCE_PASSWORD}
    hibernate-orm:
      log:
        sql: false
        bind-parameters: false
    grpc:
      server:
        host: ${WO_GRPC_HOST}
        port: ${WO_GRPC_PORT}
        enable-reflection-service: true
