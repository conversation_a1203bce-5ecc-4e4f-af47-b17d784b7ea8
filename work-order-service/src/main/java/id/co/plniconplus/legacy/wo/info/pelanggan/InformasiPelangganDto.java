package id.co.plniconplus.legacy.wo.info.pelanggan;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 04/08/2025
 */
public record InformasiPelangganDto(
        String idPelanggan,
        String nama<PERSON>ela<PERSON>gan,
        String alamatPelanggan,
        String noTelpPelanggan,
        String noHpPelanggan) {

    public static InformasiPelangganDto fromEntity(InformasiPelanggan informasiPelanggan) {
        return new InformasiPelangganDto(
                informasiPelanggan.idPelanggan,
                informasiPelanggan.namaPelanggan,
                informasiPelanggan.alamatPelanggan,
                informasiPelanggan.noTelpPelanggan,
                informasiPelanggan.noHpPelanggan);
    }

    public InformasiPelanggan toEntity() {
        InformasiPelanggan informasiPelanggan = new InformasiPelanggan();
        informasiPelanggan.idPelanggan = this.idPelanggan;
        informasiPelanggan.namaPelanggan = this.namaPelanggan;
        informasiPelanggan.alamatPelanggan = this.alamatPelanggan;
        informasiPelanggan.noTelpPelanggan = this.noTelpPelanggan;
        informasiPelanggan.noHpPelanggan = this.noHpPelanggan;
        return informasiPelanggan;
    }
}
