package id.co.plniconplus.legacy.wo.info.petugas;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 04/08/2025
 */
public record InformasiPetugasDto(
        String kodeVendor,
        String noSuratPerintahKerja,
        String noBeritaAcara) {

    public static InformasiPetugasDto fromEntity(InformasiPetugas informasiPetugas) {
        return new InformasiPetugasDto(
                informasiPetugas.kodeVendor,
                informasiPetugas.noSuratPerintahKerja,
                informasiPetugas.noBeritaAcara);
    }

    public InformasiPetugas toEntity() {
        InformasiPetugas informasiPetugas = new InformasiPetugas();
        informasiPetugas.kodeVendor = this.kodeVendor;
        informasiPetugas.noSuratPerintahKerja = this.noSuratPerintahKerja;
        informasiPetugas.noBeritaAcara = this.noBeritaAcara;
        return informasiPetugas;
    }
}
