package id.co.plniconplus.legacy.wo.info.pelapor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 04/08/2025
 */
public record InformasiPelaporDto(
        String namaPemohon,
        String alamatPemohon,
        String noTelpPemohon,
        String noHpPemohon) {

    public static InformasiPelaporDto fromEntity(InformasiPelapor informasiPelapor) {
        return new InformasiPelaporDto(
                informasiPelapor.namaPemohon,
                informasiPelapor.alamatPemohon,
                informasiPelapor.noTelpPemohon,
                informasiPelapor.noHpPemohon);
    }

    public InformasiPelapor toEntity() {
        InformasiPelapor informasiPelapor = new InformasiPelapor();
        informasiPelapor.namaPemohon = this.namaPemohon;
        informasiPelapor.alamatPemohon = this.alamatPemohon;
        informasiPelapor.noTelpPemohon = this.noTelpPemohon;
        informasiPelapor.noHpPemohon = this.noHpPemohon;
        return informasiPelapor;
    }
}
