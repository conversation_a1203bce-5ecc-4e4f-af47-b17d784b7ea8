package id.co.plniconplus.legacy.wo.info.pelapor;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 30/07/2025
 */
@Embeddable
public class InformasiPelapor extends PanacheEntityBase {

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_PEMOHON")
    public String namaPemohon;

    @Size(max = 1000)
    @Column(length = 1000, name = "ALAMAT_PEMOHON")
    public String alamatPemohon;

    @Size(max = 30)
    @Column(length = 30, name = "NOTELP_PEMOHON")
    public String noTelpPemohon;

    @Size(max = 30)
    @Column(length = 30, name = "NOHP_PEMOHON")
    public String noHpPemohon;
}
