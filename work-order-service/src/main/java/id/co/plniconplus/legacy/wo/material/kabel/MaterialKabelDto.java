package id.co.plniconplus.legacy.wo.material.kabel;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 04/08/2025
 */
public record MaterialKabelDto(
        String kodeKabel,
        String kodePembKabel,
        String merekKabel,
        String typeKabel,
        String noKabel,
        String noPabrikKabel,
        String noRegisterKabel,
        String tahunBuatKabel,
        String tahunTeraKabel,
        Integer jumlahKabel) {

    public static MaterialKabelDto fromEntity(MaterialKabel materialKabel) {
        return new MaterialKabelDto(materialKabel.kodeKabel,
                materialKabel.kodePembKabel,
                materialKabel.merekKabel,
                materialKabel.typeKabel,
                materialKabel.noKabel,
                materialKabel.noPabrikKabel,
                materialKabel.noRegisterKabel,
                materialKabel.tahunBuatKabel,
                materialKabel.tahunTeraKabel,
                materialKabel.jumlahKabel);
    }

    public MaterialKabel toEntity() {
        MaterialKabel materialKabel = new MaterialKabel();
        materialKabel.kodeKabel = this.kodeKabel;
        materialKabel.kodePembKabel = this.kodePembKabel;
        materialKabel.merekKabel = this.merekKabel;
        materialKabel.typeKabel = this.typeKabel;
        materialKabel.noKabel = this.noKabel;
        materialKabel.noPabrikKabel = this.noPabrikKabel;
        materialKabel.noRegisterKabel = this.noRegisterKabel;
        materialKabel.tahunBuatKabel = this.tahunBuatKabel;
        materialKabel.tahunTeraKabel = this.tahunTeraKabel;
        materialKabel.jumlahKabel = this.jumlahKabel;
        return materialKabel;
    }
}
