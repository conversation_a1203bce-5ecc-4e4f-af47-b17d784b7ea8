package id.co.plniconplus.legacy.wo.material.trafo.tegangan;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 30/07/2025
 */
@Embeddable
public class MaterialTrafoTegangan extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_PT")
    public String kodeTrafoTegangan;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_PT")
    public String kodePembTrafoTegangan;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_PT")
    public String merekTrafoTegangan;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_PT")
    public String typeTrafoTegangan;

    @Size(max = 24)
    @Column(length = 24, name = "NO_PT")
    public String noTrafoTegangan;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_PT")
    public String noPabrikTrafoTegangan;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_PT")
    public String noRegisterTrafoTegangan;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_PT")
    public String tahunBuatTrafoTegangan;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_PT")
    public String tahunTeraTrafoTegangan;

    @Column(name = "QTY_PT")
    public Integer jumlahTrafoTegangan;
}
