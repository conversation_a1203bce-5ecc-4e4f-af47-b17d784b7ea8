package id.co.plniconplus.legacy.wo.material.pembatas;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 06/08/2025
 */
public record MaterialPembatasDto(
        String kodeArusPembatas,
        String kodePembPembatas,
        String merekPembatas,
        String jenisPembatas,
        String ukuranSettingPembatas,
        String fasaPembatas,
        String tegangPembatas,
        String typePembatas,
        String noPembatas,
        String noPabrikPembatas,
        String noRegistrasiPembatas,
        Long arusPembatas,
        String tahunBuatPembatas,
        String tahunTeraPembatas,
        Integer jumlahArusPembatas) {

    public static MaterialPembatasDto fromEntity(MaterialPembatas materialPembatas) {
        return new MaterialPembatasDto(
                materialPembatas.kodeArusPembatas,
                materialPembatas.kodePembPembatas,
                materialPembatas.merekPembatas,
                materialPembatas.jenisPembatas,
                materialPembatas.ukuranSettingPembatas,
                materialPembatas.fasaPembatas,
                materialPembatas.tegangPembatas,
                materialPembatas.typePembatas,
                materialPembatas.noPembatas,
                materialPembatas.noPabrikPembatas,
                materialPembatas.noRegistrasiPembatas,
                materialPembatas.arusPembatas,
                materialPembatas.tahunBuatPembatas,
                materialPembatas.tahunTeraPembatas,
                materialPembatas.jumlahArusPembatas);
    }

    public MaterialPembatas toEntity() {
        MaterialPembatas materialPembatas = new MaterialPembatas();
        materialPembatas.kodeArusPembatas = this.kodeArusPembatas;
        materialPembatas.kodePembPembatas = this.kodePembPembatas;
        materialPembatas.merekPembatas = this.merekPembatas;
        materialPembatas.jenisPembatas = this.jenisPembatas;
        materialPembatas.ukuranSettingPembatas = this.ukuranSettingPembatas;
        materialPembatas.fasaPembatas = this.fasaPembatas;
        materialPembatas.tegangPembatas = this.tegangPembatas;
        materialPembatas.typePembatas = this.typePembatas;
        materialPembatas.noPembatas = this.noPembatas;
        materialPembatas.noPabrikPembatas = this.noPabrikPembatas;
        materialPembatas.noRegistrasiPembatas = this.noRegistrasiPembatas;
        materialPembatas.arusPembatas = this.arusPembatas;
        materialPembatas.tahunBuatPembatas = this.tahunBuatPembatas;
        materialPembatas.tahunTeraPembatas = this.tahunTeraPembatas;
        materialPembatas.jumlahArusPembatas = this.jumlahArusPembatas;
        return materialPembatas;
    }
}
