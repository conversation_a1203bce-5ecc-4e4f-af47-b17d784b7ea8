package id.co.plniconplus.legacy.wo.material.kwh;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 29/07/2025
 */
@Embeddable
public class MaterialKwh extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_KWH")
    public String kodeKwh;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMBMETER_KWH")
    public String kodePembMeterKwh;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_KWH")
    public String merekKwh;

    @Size(max = 20)
    @Column(length = 20, name = "TYPE_KWH")
    public String typeKwh;

    @Size(max = 24)
    @Column(length = 24, name = "NOMETER_KWH")
    public String noMeterKwh;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_KWH")
    public String noPabrikKwh;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_KWH")
    public String noRegisterKwh;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_KWH")
    public String tahunBuatKwh;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_KWH")
    public String tahunTeraKwh;

    @Column(name = "QTY_KWH")
    public Integer jumlahKwh;
}
