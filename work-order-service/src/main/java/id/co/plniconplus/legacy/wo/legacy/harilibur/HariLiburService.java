package id.co.plniconplus.legacy.wo.legacy.harilibur;

import id.co.plniconplus.legacy.harilibur.HariLiburBetweenRequest;
import id.co.plniconplus.legacy.harilibur.HariLiburResponse;
import id.co.plniconplus.legacy.harilibur.HariLiburResponseList;
import id.co.plniconplus.legacy.harilibur.MutinyHariLiburViewGrpc;
import id.co.plniconplus.util.GrpcUtil;
import io.quarkus.cache.CacheResult;
import io.quarkus.grpc.GrpcClient;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 10/09/2025
 */
@ApplicationScoped
public class HariLiburService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;

    @GrpcClient("hari-libur")
    MutinyHariLiburViewGrpc.MutinyHariLiburViewStub hariLiburService;

    @CacheResult(cacheName = "hari-libur-cache")
    public Uni<Map<String, List<HariLiburResponse>>> getHariLiburCache(LocalDateTime start, LocalDateTime end) {
        HariLiburBetweenRequest request = HariLiburBetweenRequest.newBuilder()
                .setStartDate(start.format(DATE_FORMATTER))
                .setEndDate(end.format(DATE_FORMATTER)).build();
        return GrpcUtil.withRetry(hariLiburService.getListHariLiburBetween(request)
                        .map(HariLiburResponseList::getHariLiburListList), "hari-libur-between")
                .map(list -> Map.of("holidays", list));
    }
}
