package id.co.plniconplus.legacy.wo.material.saklarwaktu;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 06/08/2025
 */
public record MaterialSaklarWaktuDto(
        String kodeSaklarWaktu,
        String kodePembSaklarWaktu,
        String merekSaklarWaktu,
        String typeSaklarWaktu,
        String noSaklarWaktu,
        String noPabrikSaklarWaktu,
        String noRegisterSaklarWaktu,
        String tahunBuatSaklarWaktu,
        String tahunTeraSaklarWaktu,
        String teganganSaklarWaktu,
        String arusSaklarWaktu,
        String ukuranSettingSaklarWaktu,
        String fasaBatasSaklarWaktu,
        Integer jumlahSaklarWaktu) {

    public static MaterialSaklarWaktuDto fromEntity(MaterialSaklarWaktu materialSaklarWaktu) {
        return new MaterialSaklarWaktuDto(
                materialSaklarWaktu.kodeSaklarWaktu,
                materialSaklarWaktu.kodePembSaklarWaktu,
                materialSaklarWaktu.merekSaklarWaktu,
                materialSaklarWaktu.typeSaklarWaktu,
                materialSaklarWaktu.noSaklarWaktu,
                materialSaklarWaktu.noPabrikSaklarWaktu,
                materialSaklarWaktu.noRegisterSaklarWaktu,
                materialSaklarWaktu.tahunBuatSaklarWaktu,
                materialSaklarWaktu.tahunTeraSaklarWaktu,
                materialSaklarWaktu.teganganSaklarWaktu,
                materialSaklarWaktu.arusSaklarWaktu,
                materialSaklarWaktu.ukuranSettingSaklarWaktu,
                materialSaklarWaktu.fasaBatasSaklarWaktu,
                materialSaklarWaktu.jumlahSaklarWaktu);
    }

    public MaterialSaklarWaktu toEntity() {
        MaterialSaklarWaktu materialSaklarWaktu = new MaterialSaklarWaktu();
        materialSaklarWaktu.kodeSaklarWaktu = this.kodeSaklarWaktu;
        materialSaklarWaktu.kodePembSaklarWaktu = this.kodePembSaklarWaktu;
        materialSaklarWaktu.merekSaklarWaktu = this.merekSaklarWaktu;
        materialSaklarWaktu.typeSaklarWaktu = this.typeSaklarWaktu;
        materialSaklarWaktu.noSaklarWaktu = this.noSaklarWaktu;
        materialSaklarWaktu.noPabrikSaklarWaktu = this.noPabrikSaklarWaktu;
        materialSaklarWaktu.noRegisterSaklarWaktu = this.noRegisterSaklarWaktu;
        materialSaklarWaktu.tahunBuatSaklarWaktu = this.tahunBuatSaklarWaktu;
        materialSaklarWaktu.tahunTeraSaklarWaktu = this.tahunTeraSaklarWaktu;
        materialSaklarWaktu.teganganSaklarWaktu = this.teganganSaklarWaktu;
        materialSaklarWaktu.arusSaklarWaktu = this.arusSaklarWaktu;
        materialSaklarWaktu.ukuranSettingSaklarWaktu = this.ukuranSettingSaklarWaktu;
        materialSaklarWaktu.fasaBatasSaklarWaktu = this.fasaBatasSaklarWaktu;
        materialSaklarWaktu.jumlahSaklarWaktu = this.jumlahSaklarWaktu;
        return materialSaklarWaktu;
    }
}
