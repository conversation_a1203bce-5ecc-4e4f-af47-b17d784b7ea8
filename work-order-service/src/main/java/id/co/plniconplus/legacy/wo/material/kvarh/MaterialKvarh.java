package id.co.plniconplus.legacy.wo.material.kvarh;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 29/07/2025
 */
@Embeddable
public class MaterialKvarh extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_KVARH")
    public String kodeKvarh;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMBMETER_KVARH")
    public String kodePembMeterKvarh;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_KVARH")
    public String merekKvarh;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_KVARH")
    public String typeKvarh;

    @Size(max = 24)
    @Column(length = 24, name = "NOMETER_KVARH")
    public String noMeterKvarh;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_KVARH")
    public String noPabrikKvarh;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_KVARH")
    public String noRegisterKvarh;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_KVARH")
    public String tahunBuatKvarh;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_KVARH")
    public String tahunTeraKvarh;

    @Column(name = "QTY_KVARH")
    public Integer jumlahKvarh;
}
