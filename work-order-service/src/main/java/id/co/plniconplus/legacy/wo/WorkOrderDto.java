package id.co.plniconplus.legacy.wo;

import id.co.plniconplus.legacy.wo.info.pelanggan.InformasiPelangganDto;
import id.co.plniconplus.legacy.wo.info.pelapor.InformasiPelaporDto;
import id.co.plniconplus.legacy.wo.info.petugas.InformasiPetugasDto;
import id.co.plniconplus.legacy.wo.material.kabel.MaterialKabelDto;
import id.co.plniconplus.legacy.wo.material.kvamaks.MaterialKvamaksDto;
import id.co.plniconplus.legacy.wo.material.kvarh.MaterialKvarhDto;
import id.co.plniconplus.legacy.wo.material.kwh.MaterialKwhDto;
import id.co.plniconplus.legacy.wo.material.modem.MaterialModemDto;
import id.co.plniconplus.legacy.wo.material.pembatas.MaterialPembatasDto;
import id.co.plniconplus.legacy.wo.material.saklarwaktu.MaterialSaklarWaktuDto;
import id.co.plniconplus.legacy.wo.material.trafo.arus.MaterialTrafoArusDto;
import id.co.plniconplus.legacy.wo.material.trafo.tegangan.MaterialTrafoTeganganDto;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 06/08/2025
 */
public record WorkOrderDto(
        InformasiPelangganDto informasiPelanggan,
        InformasiPelaporDto informasiPelapor,
        InformasiPetugasDto informasiPetugas,
        MaterialKabelDto materialKabel,
        MaterialKvarhDto materialKvarh,
        MaterialKvamaksDto materialKvamaks,
        MaterialKwhDto materialKwh,
        MaterialPembatasDto materialPembatas,
        MaterialSaklarWaktuDto materialSaklarWaktu,
        MaterialTrafoArusDto materialTrafoArus,
        MaterialTrafoTeganganDto materialTrafoTegangan,
        MaterialModemDto materialModem,
        LocalDateTime tanggalAgenda,
        LocalDateTime tanggalIsiMaterial,
        LocalDateTime tanggalLunas,
        LocalDateTime tanggalRemaja,
        LocalDateTime tanggalRestitusi,
        String status,
        String tarif,
        String tarifLama,
        String daya,
        String dayaLama,
        String jenisTransaksi,
        String kodePaket,
        String noWorkOrder,
        LocalDateTime tanggalWorkOrder,
        LocalDateTime tanggalCatat,
        Integer messagingStatus,
        Integer hariSla,
        LocalDateTime tanggalPerintahKerja,
        LocalDateTime tanggalPasangSaklarWaktu) {

    public static WorkOrderDto fromEntity(WorkOrder workOrder) {
        return new WorkOrderDto(
                InformasiPelangganDto.fromEntity(workOrder.informasiPelanggan),
                InformasiPelaporDto.fromEntity(workOrder.informasiPelapor),
                InformasiPetugasDto.fromEntity(workOrder.informasiPetugas),
                MaterialKabelDto.fromEntity(workOrder.materialKabel),
                MaterialKvarhDto.fromEntity(workOrder.materialKvarh),
                MaterialKvamaksDto.fromEntity(workOrder.materialKvamaks),
                MaterialKwhDto.fromEntity(workOrder.materialKwh),
                MaterialPembatasDto.fromEntity(workOrder.materialPembatas),
                MaterialSaklarWaktuDto.fromEntity(workOrder.materialSaklarWaktu),
                MaterialTrafoArusDto.fromEntity(workOrder.materialTrafoArus),
                MaterialTrafoTeganganDto.fromEntity(workOrder.materialTrafoTegangan),
                MaterialModemDto.fromEntity(workOrder.materialModem),
                workOrder.tanggalAgenda,
                workOrder.tanggalIsiMaterial,
                workOrder.tanggalLunas,
                workOrder.tanggalRemaja,
                workOrder.tanggalRestitusi,
                workOrder.status,
                workOrder.tarif,
                workOrder.tarifLama,
                workOrder.daya,
                workOrder.dayaLama,
                workOrder.jenisTransaksi,
                workOrder.kodePaket,
                workOrder.noWorkOrder,
                workOrder.tanggalWorkOrder,
                workOrder.tanggalCatat,
                workOrder.messagingStatus,
                workOrder.hariSla,
                workOrder.tanggalPerintahKerja,
                workOrder.tanggalPasangSaklarWaktu);
    }

    public WorkOrder toEntity() {
        WorkOrder workOrder = new WorkOrder();
        workOrder.tanggalAgenda = this.tanggalAgenda;
        workOrder.tanggalIsiMaterial = this.tanggalIsiMaterial;
        workOrder.tanggalLunas = this.tanggalLunas;
        workOrder.tanggalRemaja = this.tanggalRemaja;
        workOrder.tanggalRestitusi = this.tanggalRestitusi;
        workOrder.status = this.status;
        workOrder.tarif = this.tarif;
        workOrder.tarifLama = this.tarifLama;
        workOrder.daya = this.daya;
        workOrder.dayaLama = this.dayaLama;
        workOrder.jenisTransaksi = this.jenisTransaksi;
        workOrder.kodePaket = this.kodePaket;
        workOrder.noWorkOrder = this.noWorkOrder;
        workOrder.tanggalWorkOrder = this.tanggalWorkOrder;
        workOrder.tanggalCatat = this.tanggalCatat;
        workOrder.messagingStatus = this.messagingStatus;
        workOrder.hariSla = this.hariSla;
        workOrder.tanggalPerintahKerja = this.tanggalPerintahKerja;
        workOrder.tanggalPasangSaklarWaktu = this.tanggalPasangSaklarWaktu;
        return workOrder;
    }
}
