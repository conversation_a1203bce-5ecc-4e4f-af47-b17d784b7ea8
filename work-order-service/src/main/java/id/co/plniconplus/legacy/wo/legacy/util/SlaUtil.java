package id.co.plniconplus.legacy.wo.legacy.util;

import id.co.plniconplus.legacy.harilibur.HariLiburResponse;
import id.co.plniconplus.legacy.wo.JenisTransaksi;
import id.co.plniconplus.legacy.wo.WorkOrder;
import io.quarkus.logging.Log;
import lombok.experimental.UtilityClass;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 10/09/2025
 */
@UtilityClass
public class SlaUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param wo Work order
     * @return Hari SLA
     */
    public static String getHariSla(WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || isNotSla(wo.jenisTransaksi))
            return "-";
        return wo.hariSla.toString();
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param jenisTransaksi Jenis transaksi pelanggan
     * @return <code>true</code> jika transaksi termasuk jenis yang memiliki SLA
     * <code>false</code> jika tidak
     */
    public static boolean isNotSla(String jenisTransaksi) {
        return !JenisTransaksi.FLAG_SLA_ENABLED.contains(jenisTransaksi);
    }

    public static long calculateWorkingDays(LocalDateTime start, LocalDateTime end,
                                            Map<String, List<HariLiburResponse>> holidayMap) {
        List<HariLiburResponse> holidays = holidayMap.get("holidays");
        long totalDays = ChronoUnit.DAYS.between(start, end);
        long weekendDays = calculateWeekendDays(start, end);
        long holidayDays = calculateHolidayDays(start, end, holidays);
        return Math.max(totalDays - weekendDays - holidayDays, 0L);
    }

    private long calculateWeekendDays(LocalDateTime start, LocalDateTime end) {
        long days = ChronoUnit.DAYS.between(start, end);
        long weekends = 0;
        for (int i = 0; i <= days; i++) {
            LocalDateTime currentDate = start.plusDays(i);
            DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                weekends++;
            }
        }
        return weekends;
    }

    private long calculateHolidayDays(LocalDateTime start, LocalDateTime end, List<HariLiburResponse> holidays) {
        if (holidays == null || holidays.isEmpty())
            return 0;
        return holidays.stream()
                .filter(holiday -> {
                    try {
                        LocalDate holidayDate = LocalDate.parse(holiday.getTanggalLibur(), DATE_FORMATTER);
                        LocalDateTime holidayDateTime = holidayDate.atStartOfDay();
                        return !holidayDateTime.isBefore(start) && !holidayDateTime.isAfter(end) &&
                                holidayDateTime.getDayOfWeek() != DayOfWeek.SATURDAY &&
                                holidayDateTime.getDayOfWeek() != DayOfWeek.SUNDAY;
                    } catch (DateTimeParseException e) {
                        Log.warnv("Invalid holiday date format: {0}", e.getMessage());
                        return false;
                    }
                })
                .count();
    }
}
