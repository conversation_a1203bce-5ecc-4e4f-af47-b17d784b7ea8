package id.co.plniconplus.legacy.wo.material.kvarh;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 06/08/2025
 */
public record MaterialKvarhDto(
        String kodeKvarh,
        String kodePembMeterKvarh,
        String merekKvarh,
        String typeKvarh,
        String noMeterKvarh,
        String noPabrikKvarh,
        String noRegisterKvarh,
        String tahunBuatKvarh,
        String tahunTeraKvarh,
        Integer jumlahKvarh) {

    public static MaterialKvarhDto fromEntity(MaterialKvarh materialKvarh) {
        return new MaterialKvarhDto(
                materialKvarh.kodeKvarh,
                materialKvarh.kodePembMeterKvarh,
                materialKvarh.merekKvarh,
                materialKvarh.typeKvarh,
                materialKvarh.noMeterKvarh,
                materialKvarh.noPabrikKvarh,
                materialKvarh.noRegisterKvarh,
                materialKvarh.tahunBuatKvarh,
                materialKvarh.tahunTeraKvarh,
                materialKvarh.jumlahKvarh);
    }

    public MaterialKvarh toEntity() {
        MaterialKvarh materialKvarh = new MaterialKvarh();
        materialKvarh.kodeKvarh = this.kodeKvarh;
        materialKvarh.kodePembMeterKvarh = this.kodePembMeterKvarh;
        materialKvarh.merekKvarh = this.merekKvarh;
        materialKvarh.typeKvarh = this.typeKvarh;
        materialKvarh.noMeterKvarh = this.noMeterKvarh;
        materialKvarh.noPabrikKvarh = this.noPabrikKvarh;
        materialKvarh.noRegisterKvarh = this.noRegisterKvarh;
        materialKvarh.tahunBuatKvarh = this.tahunBuatKvarh;
        materialKvarh.tahunTeraKvarh = this.tahunTeraKvarh;
        materialKvarh.jumlahKvarh = this.jumlahKvarh;
        return materialKvarh;
    }
}
