package id.co.plniconplus.legacy.wo;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 29/07/2025
 */
@Embeddable
public class WorkOrderId implements Serializable {

    @NotEmpty
    @Size(max = 30)
    @Column(length = 30, name = "NOAGENDA", nullable = false)
    public String noAgenda;

    @NotEmpty
    @Size(max = 5)
    @Column(length = 5, name = "UNITUP", nullable = false)
    public String unitUp;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        WorkOrderId that = (WorkOrderId) o;
        return Objects.equals(noAgenda, that.noAgenda) && Objects.equals(unitUp, that.unitUp);
    }

    @Override
    public int hashCode() {
        return Objects.hash(noAgenda, unitUp);
    }
}
