package id.co.plniconplus.legacy.wo.info.petugas;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 30/07/2025
 */
@Embeddable
public class InformasiPetugas extends PanacheEntityBase {

    @Size(max = 10)
    @Column(length = 10, name = "KODE_VENDOR")
    public String kodeVendor;

    @Size(max = 30)
    @Column(length = 30, name = "NO_SPK")
    public String noSuratPerintahKerja;

    @Size(max = 30)
    @Column(length = 30, name = "NO_BA")
    public String noBeritaAcara;

    @Size(max = 30)
    @Column(length = 30, name = "PETUGAS_PENERIMA")
    public String petugasPenerima;
}
