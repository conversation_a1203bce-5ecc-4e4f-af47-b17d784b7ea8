package id.co.plniconplus.legacy.wo.material.modem;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 30/07/2025
 */
@Embeddable
public class MaterialModem extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_MODEM")
    public String kodeModem;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMB_MODEM")
    public String kodePembModem;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_MODEM")
    public String merekModem;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_MODEM")
    public String typeModem;

    @Size(max = 24)
    @Column(length = 24, name = "NO_MODEM")
    public String noModem;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_MODEM")
    public String noPabrikModem;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_MODEM")
    public String noRegisterModem;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_MODEM")
    public String tahunBuatModem;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_MODEM")
    public String tahunTeraModem;

    @Size(max = 50)
    @Column(length = 50, name = "OPSEL")
    public String operatorSeluler;

    @Size(max = 50)
    @Column(length = 50, name = "SPEED")
    public String speed;

    @Column(name = "QTY_MODEM")
    public Integer jumlahModem;
}
