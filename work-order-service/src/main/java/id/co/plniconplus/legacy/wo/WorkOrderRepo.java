package id.co.plniconplus.legacy.wo;

import id.co.plniconplus.legacy.pesta.WoPestaDto;
import id.co.plniconplus.legacy.wo.dispatch.*;
import id.co.plniconplus.legacy.wo.info.pelanggan.tetangga.PelangganTetanggaDto;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 06/08/2025
 */
@ApplicationScoped
public class WorkOrderRepo implements PanacheRepositoryBase<WorkOrder, WorkOrderId> {

    public List<WoPestaDto> findPestaByNoAgenda(String noAgenda) {
        return find("""
                SELECT p.tangg<PERSON>,
                p.tan<PERSON>,
                p.lwbpSebelumPesta,
                p.wbpSebelumPesta,
                p.kvarhSebelumPesta,
                p.sisaKwhSebelumPesta,
                wo.tanggalLunas,
                p.email,
                wo.informasiPetugas.petugasPenerima,
                wo.tarif
                FROM WorkOrder wo
                LEFT OUTER JOIN Pesta p ON wo.workOrderId.noAgenda = p.noAgenda
                WHERE wo.workOrderId.noAgenda = ?1
                """, noAgenda)
                .project(WoPestaDto.class)
                .list();
    }

    public List<WorkOrder> findByNoAgendaAndStatusCommissioning(String noAgenda, Integer statusCommissioning) {
        return find("noAgenda = ?1 AND (statusCommissioning = ?2 OR statusCommissioning is null)",
                noAgenda, statusCommissioning).list();
    }

    public int updatePelangganTetangga(PelangganTetanggaDto dto, String noAgenda) {
        return update("""
                        UPDATE WorkOrder
                        SET idPelangganTerdekat = ?1,
                        unitUp = ?2,
                        upTerdekat = ?3,
                        apTerdekat = ?4,
                        upiTerdekat = ?5,
                        kodeGardu = ?6,
                        namaGardu = ?7,
                        noTiang = ?8
                        WHERE workOrderId.noAgenda = ?9
                        """, dto.idPelanggan(), dto.idUnitUp(), dto.upTerdekat(), dto.apTerdekat(),
                dto.upiTerdekat(), dto.kodeGardu(), dto.namaGardu(), dto.nomorTiang(), noAgenda);
    }

    public int updatePenugasan(DispatchUpdateDto dto, String noAgenda) {
        return update("""
                        UPDATE WorkOrder
                        SET status = ?1,
                        statusDate = ?2,
                        noWorkOrder = ?3,
                        tanggalWorkOrder = ?4,
                        tanggalExpire = ?5,
                        informasiPetugas.kodeVendor = ?6,
                        userId = ?7
                        WHERE workOrderId.noAgenda = ?8
                        """, dto.status(), dto.statusDate(), dto.noWo(), dto.tanggalWo(),
                dto.tanggalWoExpire(), dto.kodeVendor(), dto.idUser(), noAgenda);
    }

    public PanacheQuery<WorkOrder> findByDispatched(DispatchRequestDto dto, Sort sort) {
        StringBuilder query = new StringBuilder("""
                    SELECT wo
                    FROM WorkOrder wo
                    WHERE wo.workOrderId.unitUp = :unitUp
                    AND wo.status IN (:statusList)
                    """);
        Map<String, Object> params = new HashMap<>();
        params.put("unitUp", dto.unitUp());
        params.put("statusList", List.of("01", "02"));

        if (JenisTanggal.LUNAS.equals(dto.jenisTanggal())) {
            query.append("""
                        AND wo.tanggalLunas BETWEEN :tanggalAwal AND :tanggalAkhir
                        """);
        } else if (JenisTanggal.PK.equals(dto.jenisTanggal())) {
            query.append("""
                        AND wo.tanggalPerintahKerja BETWEEN :tanggalAwal AND :tanggalAkhir
                        """);
        } else if (JenisTanggal.ISI_MATERIAL.equals(dto.jenisTanggal())) {
            query.append("""
                        AND wo.tanggalIsiMaterial BETWEEN :tanggalAwal AND :tanggalAkhir
                        """);
        } else if (JenisTanggal.WO.equals(dto.jenisTanggal())) {
            query.append("""
                        AND wo.tanggalWorkOrder BETWEEN :tanggalAwal AND :tanggalAkhir
                        """);
        }
        if (dto.jenisTanggal() != null) {
            params.put("tanggalAwal", dto.tanggalAwal() != null ? LocalDateTime.parse(dto.tanggalAwal()) : null);
            params.put("tanggalAkhir", dto.tanggalAkhir() != null ? LocalDateTime.parse(dto.tanggalAkhir()) : null);
        }

        if (!JenisTransaksi.SEMUA.equals(dto.jenisTransaksi())) {
            query.append("""
                        AND wo.jenisTransaksi = :jenisTransaksi
                        """);
            params.put("jenisTransaksi", dto.jenisTransaksi());
        }
        if (AmiStatus.AMI.equals(dto.ami())) {
            query.append("""
                        AND wo.messagingStatus = :messagingStatusAmi
                        AND wo.materialKwh.kodePembMeterKwh in (:kodePembMeterKwhList)
                        """);
            params.put("messagingStatusAmi", dto.gcmStatus());
            params.put("kodePembMeterKwhList", List.of('L', 'R', 'S', 'F'));
        } else if (AmiStatus.NON_AMI.equals(dto.ami())) {
            query.append("""
                        AND wo.messagingStatus <> :messagingStatusNonAmi
                        """);
            params.put("messagingStatusNonAmi", dto.gcmStatus());
        }
        if (dto.kodeVendor() != null) {
            query.append("""
                        AND wo.informasiPetugas.kodeVendor = :kodeVendor
                        """);
            params.put("kodeVendor", dto.kodeVendor());
        }
        if (WoKey.NO_AGENDA.equals(dto.field())) {
            query.append("""
                        AND wo.workOrderId.noAgenda = :keyword
                        """);
        } else if (WoKey.ID_PELANGGAN.equals(dto.field())) {
            query.append("""
                        AND wo.informasiPelanggan.idPelanggan = :keyword
                        """);
        } else if (WoKey.NAMA_PELANGGAN.equals(dto.field())) {
            query.append("""
                        AND wo.informasiPelanggan.namaPelanggan = :keyword
                        """);
        } else if (WoKey.ALAMAT_PELANGGAN.equals(dto.field())) {
            query.append("""
                        AND wo.informasiPelanggan.alamatPelanggan = :keyword
                        """);
        }
        if (dto.field() != null) {
            params.put("keyword", dto.keyWord());
        }

        if (dto.gcmStatus() != null && dto.gcmStatus().toString().equals("SEMUA")) {
            query.append("""
                        AND wo.messagingStatus = :gcmStatusSemua
                        """);
            params.put("gcmStatusSemua", dto.gcmStatus());
        } else if (dto.gcmStatus() == null) {
            query.append("""
                        AND wo.messagingStatus IS NULL
                        """);
        }
        return find(query.toString(), sort, params);
    }
}
