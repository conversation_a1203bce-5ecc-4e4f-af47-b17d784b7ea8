package id.co.plniconplus.legacy.wo.info.pelanggan.tetangga;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 09/09/2025
 */
@Embeddable
public class PelangganTetangga extends PanacheEntityBase {

    @Size(max = 30)
    @Column(length = 30, name = "IDPEL_TERDEKAT")
    public String idPelangganTerdekat;

    @Size(max = 5)
    @Column(length = 5, name = "UNITUP")
    public String unitUp;

    @Size(max = 5)
    @Column(length = 5, name = "UP_TERDEKAT")
    public String upTerdekat;

    @Size(max = 5)
    @Column(length = 5, name = "AP_TERDEKAT")
    public String apTerdekat;

    @Size(max = 2)
    @Column(length = 2, name = "UPI_TERDEKAT")
    public String upiTerdekat;

    @Size(max = 30)
    @Column(length = 30, name = "KDGARDU")
    public String kodeGardu;

    @Size(max = 50)
    @Column(length = 50, name = "NAMA_GARDU")
    public String namaGardu;

    @Size(max = 35)
    @Column(length = 35, name = "NOTIANG")
    public String noTiang;
}
