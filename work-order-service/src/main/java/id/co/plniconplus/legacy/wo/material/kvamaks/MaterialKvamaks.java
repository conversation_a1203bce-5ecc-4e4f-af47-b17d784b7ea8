package id.co.plniconplus.legacy.wo.material.kvamaks;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 29/07/2025
 */
@Embeddable
public class MaterialKvamaks extends PanacheEntityBase {

    @Size(max = 20)
    @Column(length = 20, name = "KD_KVAMAKS")
    public String kodeKvamaks;

    @Size(max = 20)
    @Column(length = 20, name = "KDPEMBMETER_KVAMAKS")
    public String kodePembMeterKvamaks;

    @Size(max = 20)
    @Column(length = 20, name = "MEREK_KVAMAKS")
    public String merekKvamaks;

    @Size(max = 10)
    @Column(length = 10, name = "TYPE_KVAMAKS")
    public String typeKvamaks;

    @Size(max = 24)
    @Column(length = 24, name = "NOMETER_KVAMAKS")
    public String noMeterKvamaks;

    @Size(max = 20)
    @Column(length = 20, name = "NOPABRIK_KVAMAKS")
    public String noPabrikKvmaks;

    @Size(max = 20)
    @Column(length = 20, name = "NOREGISTER_KVAMAKS")
    public String noRegisterKvamaks;

    @Size(max = 4)
    @Column(length = 4, name = "THBUAT_KVAMAKS")
    public String tahunBuatKvamaks;

    @Size(max = 4)
    @Column(length = 4, name = "THTERA_KVAMAKS")
    public String tahunTeraKvamaks;

    @Column(name = "QTY_KVAMAKS")
    public Integer jumlahKvamaks;
}
