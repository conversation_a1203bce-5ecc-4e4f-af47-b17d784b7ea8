package id.co.plniconplus.legacy.wo.legacy.harilibur;

import id.co.plniconplus.legacy.wo.WorkOrder;
import id.co.plniconplus.legacy.wo.legacy.util.SlaUtil;
import io.smallrye.mutiny.Uni;
import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 12/09/2025
 */
@UtilityClass
public class HariLiburUtil {

    public static Uni<Integer> getDurasiHariKerja(HariLiburService hariLiburService, WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || SlaUtil.isNotSla(wo.jenisTransaksi))
            return Uni.createFrom().item(0);

        return hitungDurasiHariKerja(hariLiburService, wo.jenisTransaksi, wo.tangg<PERSON><PERSON>ema<PERSON>, wo.tanggal<PERSON><PERSON>s, wo.tanggalRestitusi)
                .onItem().transform(duration -> Math.min(duration.intValue() - 1, wo.hariSla));
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param wo Work order
     * @return Sisa order hari SLA
     */
    public static Uni<Integer> getSisaOrderHariSla(HariLiburService hariLiburService, WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || SlaUtil.isNotSla(wo.jenisTransaksi))
            return Uni.createFrom().item(0);
        return hitungDurasiHariKerja(hariLiburService, wo.jenisTransaksi, wo.tanggalRemaja, wo.tanggalLunas, wo.tanggalRestitusi)
                .onItem().transform(duration -> {
                    if (duration > wo.hariSla) {
                        return wo.hariSla - duration.intValue();
                    } else {
                        return wo.hariSla - duration.intValue() + 1;
                    }
                });
    }

    /**
     * Migrate from function FSO.V_SLA_WO
     *
     * @param jenisTransaksi Jenis transaksi pelanggan
     * @param tglRemaja      Tanggal remaja
     * @param tglLunas       Tanggal lunas
     * @param tglRestitusi   Tanggal restitusi
     * @return Durasi hari kerja
     */
    private Uni<Long> hitungDurasiHariKerja(HariLiburService hariLiburService,
                                            String jenisTransaksi,
                                            LocalDateTime tglRemaja,
                                            LocalDateTime tglLunas,
                                            LocalDateTime tglRestitusi) {
        if (SlaUtil.isNotSla(jenisTransaksi))
            return Uni.createFrom().item(0L);
        LocalDateTime endDate = Objects.requireNonNullElseGet(tglRemaja,
                () -> Objects.requireNonNullElseGet(tglRestitusi,
                        () -> LocalDateTime.now().plusDays(1L)));
        return HariLiburUtil.hitungDurasiHariKerja(hariLiburService, tglLunas, endDate);
    }

    /**
     * Migrate from function FSO.f$HitungHariNew
     *
     * @param firstDate  Tanggal pertama
     * @param secondDate Tanggal kedua
     * @return Durasi hari kerja antara tanggal pertama dan kedua
     */
    private Uni<Long> hitungDurasiHariKerja(HariLiburService hariLiburService,
                                            LocalDateTime firstDate, LocalDateTime secondDate) {
        if (firstDate == null || secondDate == null)
            return Uni.createFrom().item(0L);
        LocalDateTime tglAwal = firstDate.isBefore(secondDate) ? firstDate : secondDate;
        LocalDateTime tglAkhir = firstDate.isBefore(secondDate) ? secondDate : firstDate;
        return hariLiburService.getHariLiburCache(tglAwal, tglAkhir)
                .map(holidayMap -> SlaUtil.calculateWorkingDays(tglAwal, tglAkhir, holidayMap));
    }
}
