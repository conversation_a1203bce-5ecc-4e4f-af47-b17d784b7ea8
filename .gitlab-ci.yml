stages:
  - build
  - native-image
  - dockerize
  - deploy

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  DOCKER_DRIVER: overlay2
  REF_DATA_SERVICE_PORT: $REF_DATA_SERVICE_PORT
  REF_DATA_SERVICE_GRPC_HOST: $REF_DATA_SERVICE_GRPC_HOST
  REF_DATA_SERVICE_GRPC_PORT: $REF_DATA_SERVICE_GRPC_PORT
  AGENT_LOCATION_SERVICE_PORT: $AGENT_LOCATION_SERVICE_PORT
  AGENT_LOCATION_GRPC_HOST: $AGENT_LOCATION_GRPC_HOST
  AGENT_LOCATION_GRPC_PORT: $AGENT_LOCATION_GRPC_PORT
  WO_SERVICE_PORT: WO_SERVICE_PORT
  WO_GRPC_HOST: WO_GRPC_HOST
  WO_GRPC_PORT: WO_GRPC_PORT
  DATASOURCE_JDBC_URL: $REF_DATA_SERVICE_DATASOURCE_JDBC_URL
  DATASOURCE_USERNAME: $REF_DATA_SERVICE_DATASOURCE_USERNAME
  DATASOURCE_PASSWORD: $REF_DATA_SERVICE_DATASOURCE_PASSWORD

cache:
  paths:
    - .m2/repository

before_script:
  - echo $CI_JOB_TOKEN | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY

.jar_deploy_template:
  stage: deploy
  image: maven:3.9.11-amazoncorretto-21
  script:
    - echo "Building and deploying JAR for $MODULE_NAME..."
    - cd $MODULE_DIR
    - mvn clean deploy -DskipTests
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - $MODULE_DIR/**/*

.build_template:
  stage: build
  image: quay.io/quarkus/ubi-quarkus-native-image:23.1-java21
  script:
    - echo "Building $SERVICE_NAME..."
    - cd $SERVICE_DIR
    - ./mvnw clean install -DskipTests

.native_template:
  stage: native-image
  image: quay.io/quarkus/ubi-quarkus-native-image:23.1-java21
  script:
    - echo "Packaging native image for $SERVICE_NAME..."
    - cd $SERVICE_DIR
    - ./mvnw package -Pnative -DskipTests -Dquarkus.native.builder-image=quay.io/quarkus/ubi-quarkus-mandrel:23.1-java21
  artifacts:
    paths:
      - "$SERVICE_DIR/target/*-runner"
  needs:
    - "build_$SERVICE_DIR"

.dockerize_template:
  stage: dockerize
  image: docker:latest
  services:
    - docker:dind
  script:
    - cd $SERVICE_DIR
    - docker build -f src/main/docker/Dockerfile.native -t "$SERVICE_IMAGE:$CI_COMMIT_SHORT_SHA" .
    - docker tag "$SERVICE_IMAGE:$CI_COMMIT_SHORT_SHA" "$SERVICE_IMAGE:latest"
    - docker push "$SERVICE_IMAGE:$CI_COMMIT_SHORT_SHA"
    - docker push "$SERVICE_IMAGE:latest"
  needs:
    - "native_$SERVICE_DIR"

.deploy_template:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - echo "Deploying $SERVICE_NAME to GitLab Container Registry..."
    - echo "Image tags pushed $SERVICE_IMAGE:$CI_COMMIT_SHORT_SHA and $SERVICE_IMAGE:latest"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - "dockerize_$SERVICE_DIR"

# --- Proto Definitions Jobs ---

deploy_proto_definitions:
  extends: .jar_deploy_template
  variables:
    MODULE_NAME: "proto-definitions"
    MODULE_DIR: "proto-definitions"

# --- API Contracts Jobs ---

deploy_api_contracts:
  extends: .jar_deploy_template
  variables:
    MODULE_NAME: "api-contracts"
    MODULE_DIR: "api-contracts"

# --- Reference Data Service Jobs ---

build_reference_data_service:
  extends: .build_template
  variables:
    SERVICE_NAME: "reference-data-service"
    SERVICE_DIR: "reference-data-service"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - reference-data-service/**/*

native_reference_data_service:
  extends: .native_template
  variables:
    SERVICE_NAME: "reference-data-service"
    SERVICE_DIR: "reference-data-service"

dockerize_reference_data_service:
  extends: .dockerize_template
  variables:
    SERVICE_NAME: "reference-data-service"
    SERVICE_DIR: "reference-data-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/reference-data-service"

deploy_reference_data_service:
  extends: .deploy_template
  variables:
    SERVICE_NAME: "reference-data-service"
    SERVICE_DIR: "reference-data-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/reference-data-service"

# --- Agent Location Service Jobs ---

build_agent_location_service:
  extends: .build_template
  variables:
    SERVICE_NAME: "agent-location-service"
    SERVICE_DIR: "agent-location-service"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - agent-location-service/**/*

native_agent_location_service:
  extends: .native_template
  variables:
    SERVICE_NAME: "agent-location-service"
    SERVICE_DIR: "agent-location-service"

dockerize_agent_location_service:
  extends: .dockerize_template
  variables:
    SERVICE_NAME: "agent-location-service"
    SERVICE_DIR: "agent-location-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/agent-location-service"

deploy_agent_location_service:
  extends: .deploy_template
  variables:
    SERVICE_NAME: "agent-location-service"
    SERVICE_DIR: "agent-location-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/agent-location-service"

# --- Work Order Service Jobs ---

build_work_order_service:
  extends: .build_template
  variables:
    SERVICE_NAME: "work-order-service"
    SERVICE_DIR: "work-order-service"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      changes:
        - work-order-service/**/*

native_work_order_service:
  extends: .native_template
  variables:
    SERVICE_NAME: "work-order-service"
    SERVICE_DIR: "work-order-service"

dockerize_work_order_service:
  extends: .dockerize_template
  variables:
    SERVICE_NAME: "work-order-service"
    SERVICE_DIR: "work-order-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/work-order-service"

deploy_work_order_service:
  extends: .deploy_template
  variables:
    SERVICE_NAME: "work-order-service"
    SERVICE_DIR: "work-order-service"
    SERVICE_IMAGE: "$CI_REGISTRY_IMAGE/work-order-service"
