services:
  reference-data-service:
    container_name: reference-data-service
    image: new-fso/reference-data-service
    build:
      context: ./reference-data-service
      dockerfile: src/main/docker/Dockerfile.native
    ports:
      - "${AGENT_LOCATION_SERVICE_PORT:-8080}:8080"   # HTTP endpoint
      - "${AGENT_LOCATION_SERVICE_PORT:-9000}:9000"   # gRPC endpoint
    environment:
      - REF_DATA_SERVICE_PORT
      - REF_DATA_SERVICE_GRPC_HOST
      - REF_DATA_SERVICE_GRPC_PORT
      - DATASOURCE_JDBC_URL
      - DATASOURCE_USERNAME
      - DATASOURCE_PASSWORD
    restart: unless-stopped
    networks:
      - new_fso
  agent-location-service:
    container_name: agent-location-service
    image: new-fso/agent-location-service
    build:
      context: ./agent-location-service
      dockerfile: src/main/docker/Dockerfile.native
    ports:
      - "${AGENT_LOCATION_SERVICE_PORT:-8082}:8080"   # HTTP endpoint
      - "${AGENT_LOCATION_SERVICE_PORT:-9002}:9000"   # gRPC endpoint
    environment:
      - AGENT_LOCATION_SERVICE_PORT
      - AGENT_LOCATION_GRPC_HOST
      - AGENT_LOCATION_GRPC_PORT
      - DATASOURCE_JDBC_URL
      - DATASOURCE_USERNAME
      - DATASOURCE_PASSWORD
    restart: unless-stopped
    networks:
      - new_fso
  work-order-service:
    container_name: work-order-service
    image: new-fso/work-order-service
    build:
      context: ./work-order-service
      dockerfile: src/main/docker/Dockerfile.native
    ports:
      - "${WO_SERVICE_PORT:-8082}:8080"   # HTTP endpoint
      - "${WO_SERVICE_PORT:-9002}:9000"   # gRPC endpoint
    environment:
      - WO_SERVICE_PORT
      - WO_GRPC_HOST
      - WO_GRPC_PORT
      - REF_DATA_GRPC_HOST
      - REF_DATA_GRPC_PORT
      - CAFFEINE_CACHE_MAX_SIZE
      - CAFFEINE_CACHE_EXPIRE_AFTER_WRITE
      - DATASOURCE_JDBC_URL
      - DATASOURCE_USERNAME
      - DATASOURCE_PASSWORD
    restart: unless-stopped
    networks:
      - new_fso
