package id.co.plniconplus.util;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/4/2025
 */
@UtilityClass
public class GrpcUtil {

    private final Map<String, Object> fallbacks = Map.of(
            "daya-fasa", Collections.emptyList(),
            "hari-libur-between", Collections.emptyList(),
            "tasrip", Collections.emptyList(),
            "gcm-status", "",
            "nama-up", "",
            "agent-location", Uni.createFrom().item(null)
    );

    public static <T> Uni<T> withRetry(Uni<T> uni, String serviceName) {
        return uni
                .ifNoItem().after(Duration.ofSeconds(5)).fail()
                .onFailure().retry().withBackOff(Duration.ofSeconds(1), Duration.ofSeconds(5)).atMost(3)
                .onFailure().invoke(failure ->
                        Log.errorv("gRPC call to {0} failed: {1}", serviceName, failure.getMessage()))
                .onFailure().recoverWithItem(() -> {
                    Log.warnv("Using fallback for {0} service after retries failed", serviceName);
                    return getFallbackForService(serviceName);
                });
    }

    @SuppressWarnings("unchecked")
    private <T> T getFallbackForService(String serviceName) {
        return (T) fallbacks.getOrDefault(serviceName, null);
    }
}
