package id.co.plniconplus.util;

import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/29/2025
 */
@UtilityClass
public class ResponseHandler {

    public static Map<String, String> generateResponse(String message, Object entity) {
        Map<String, String> response = new HashMap<>();
        response.put("message", message);
        if (entity != null) {
            response.put("data", entity.toString());
        } else {
            response.put("data", "");
        }
        return response;
    }
}
